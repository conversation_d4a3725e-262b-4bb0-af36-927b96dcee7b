'use client';

import { useState } from 'react';
import { format, addMonths, subMonths, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameMonth, isSameDay, isToday } from 'date-fns';
import { arSA } from 'date-fns/locale';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface OvulationCalendarProps {
  lastPeriodDate?: Date;
  ovulationDate?: Date;
  fertileWindowStart?: Date;
  fertileWindowEnd?: Date;
  nextPeriodDate?: Date;
  initialMonth?: Date;
  className?: string;
}

export function OvulationCalendar({
  lastPeriodDate,
  ovulationDate,
  fertileWindowStart,
  fertileWindowEnd,
  nextPeriodDate,
  initialMonth,
  className
}: OvulationCalendarProps) {
  const [currentMonth, setCurrentMonth] = useState(initialMonth || new Date());

  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(monthStart);
  const startDate = startOfWeek(monthStart, { weekStartsOn: 6 }); // Saturday
  const endDate = endOfWeek(monthEnd, { weekStartsOn: 6 });

  // Days of week headers (Arabic)
  const daysOfWeek = ['السبت', 'الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة'];

  // Generate all days for the calendar
  const calendarDays = [];
  let day = startDate;
  while (day <= endDate) {
    calendarDays.push(day);
    day = addDays(day, 1);
  }

  const getDayType = (day: Date) => {
    if (lastPeriodDate && isSameDay(day, lastPeriodDate)) {
      return 'period-start';
    }
    if (ovulationDate && isSameDay(day, ovulationDate)) {
      return 'ovulation';
    }
    if (fertileWindowStart && fertileWindowEnd &&
        day >= fertileWindowStart && day <= fertileWindowEnd) {
      return 'fertile';
    }
    if (nextPeriodDate && isSameDay(day, nextPeriodDate)) {
      return 'next-period';
    }
    return 'normal';
  };

  const getDayClasses = (day: Date) => {
    const dayType = getDayType(day);
    const baseClasses = "aspect-square w-full min-h-[40px] max-h-[60px] flex items-center justify-center text-sm font-semibold rounded-lg transition-all duration-200 hover:scale-105 cursor-pointer border border-transparent";

    if (!isSameMonth(day, monthStart)) {
      return cn(baseClasses, "text-gray-300 hover:text-gray-400 bg-gray-50/50");
    }

    let todayClasses = "";
    if (isToday(day)) {
      todayClasses = "ring-2 ring-blue-500 ring-offset-1";
    }

    switch (dayType) {
      case 'period-start':
        return cn(baseClasses, "bg-red-500 text-white shadow-md hover:bg-red-600 border-red-600", todayClasses);
      case 'ovulation':
        return cn(baseClasses, "bg-red-600 text-white shadow-md hover:bg-red-700 border-red-700", todayClasses);
      case 'fertile':
        return cn(baseClasses, "bg-teal-400 text-white shadow-md hover:bg-teal-500 border-teal-500", todayClasses);
      case 'next-period':
        return cn(baseClasses, "bg-purple-500 text-white shadow-md hover:bg-purple-600 border-purple-600", todayClasses);
      default:
        return cn(baseClasses, "text-gray-700 hover:bg-gray-100 bg-gray-50 border-gray-200", todayClasses);
    }
  };



  const nextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  const prevMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  return (
    <Card className={cn("w-full max-w-md mx-auto", className)}>
      <CardContent className="p-3 sm:p-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <Button
            variant="outline"
            size="sm"
            onClick={prevMonth}
            className="h-8 w-8 p-0"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
          
          <h2 className="text-lg sm:text-xl font-semibold text-center">
            {format(currentMonth, 'MMMM yyyy', { locale: arSA })}
          </h2>
          
          <Button
            variant="outline"
            size="sm"
            onClick={nextMonth}
            className="h-8 w-8 p-0"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
        </div>

        {/* Days of week header */}
        <div className="grid grid-cols-7 gap-2 mb-4">
          {daysOfWeek.map((day) => (
            <div
              key={day}
              className="aspect-square min-h-[40px] max-h-[50px] flex items-center justify-center text-xs font-bold text-gray-700 bg-gray-100 rounded-lg border border-gray-200"
            >
              <span className="text-center leading-tight">{day}</span>
            </div>
          ))}
        </div>

        {/* Calendar grid */}
        <div className="grid grid-cols-7 gap-2">
          {calendarDays.map((day) => (
            <div
              key={day.toString()}
              className={getDayClasses(day)}
            >
              <span className="text-center leading-none">{format(day, 'd', { locale: arSA })}</span>
            </div>
          ))}
        </div>

        {/* Legend */}
        {(lastPeriodDate || ovulationDate || fertileWindowStart) && (
          <div className="mt-4 pt-4 border-t">
            <div className="flex flex-wrap justify-center gap-3 text-xs">
              {lastPeriodDate && (
                <div className="flex items-center gap-1.5">
                  <div className="w-3 h-3 bg-red-500 rounded border border-red-600"></div>
                  <span className="text-gray-700">بداية الحيض</span>
                </div>
              )}
              {fertileWindowStart && (
                <div className="flex items-center gap-1.5">
                  <div className="w-3 h-3 bg-teal-400 rounded border border-teal-500"></div>
                  <span className="text-gray-700">فترة الخصوبة</span>
                </div>
              )}
              {ovulationDate && (
                <div className="flex items-center gap-1.5">
                  <div className="w-3 h-3 bg-red-600 rounded border border-red-700"></div>
                  <span className="text-gray-700">يوم الإباضة</span>
                </div>
              )}
              {nextPeriodDate && (
                <div className="flex items-center gap-1.5">
                  <div className="w-3 h-3 bg-purple-500 rounded border border-purple-600"></div>
                  <span className="text-gray-700">الدورة القادمة</span>
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
