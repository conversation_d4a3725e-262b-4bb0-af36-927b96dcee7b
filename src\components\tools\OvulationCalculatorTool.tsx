
'use client';

import { useState } from 'react';
import { useForm, Control } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Target, Leaf, CircleHelp, Droplet, TestTube } from 'lucide-react';
import { format, addDays, subDays } from 'date-fns';
import { arSA } from 'date-fns/locale';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { OvulationCalendar } from './OvulationCalendar';


const requiredNumber = (message = "هذا الحقل مطلوب.") => 
  z.coerce.number({ required_error: message, invalid_type_error: "الرجاء إدخال رقم صحيح." });

const FormSchema = z.object({
  day: requiredNumber('اليوم مطلوب'),
  month: requiredNumber('الشهر مطلوب'),
  cycleLength: requiredNumber('طول الدورة مطلوب').int().min(20, "طول الدورة عادة بين 20-45 يومًا.").max(45, "طول الدورة عادة بين 20-45 يومًا.").default(28),
});

type FormValues = z.infer<typeof FormSchema>;

interface Result {
  lastPeriodDate: Date;
  ovulationDate: Date;
  fertileWindowStart: Date;
  fertileWindowEnd: Date;
  nextPeriodDate: Date;
  testDay: Date;
}

const DateFields = ({ control }: { control: Control<FormValues> }) => {
    const months = Array.from({ length: 12 }, (_, i) => i + 1);
    const days = Array.from({ length: 31 }, (_, i) => i + 1);

    return (
    <div className="space-y-2">
        <FormLabel>أول يوم من آخر دورة شهرية</FormLabel>
        <div className="grid grid-cols-2 gap-2">
            <FormField
              control={control}
              name="day"
              render={({ field }) => (
                <FormItem>
                  <Select onValueChange={field.onChange} value={field.value?.toString()}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="اليوم" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {days.map(d => <SelectItem key={d} value={String(d)}>{d}</SelectItem>)}
                    </SelectContent>
                  </Select>
                  <FormMessage className="col-span-3"/>
                </FormItem>
              )}
            />
            <FormField
              control={control}
              name="month"
              render={({ field }) => (
                <FormItem>
                  <Select onValueChange={field.onChange} value={field.value?.toString()}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="الشهر" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {months.map(m => <SelectItem key={m} value={String(m)}>{m}</SelectItem>)}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
        </div>
    </div>
)};

const ResultInfoCard = ({ date, title, colorClass, icon: Icon }: { date: Date, title: string, colorClass: string, icon: React.ElementType }) => (
    <div className={`text-center p-3 rounded-lg border-b-4 ${colorClass}`}>
        <p className="text-xl font-bold">{format(date, 'd', { locale: arSA })}</p>
        <p className="text-xs">{format(date, 'MMMM yyyy', { locale: arSA })}</p>
        <div className="flex items-center justify-center gap-1 mt-2 text-xs font-medium">
            <Icon className="h-4 w-4" />
            <span>{title}</span>
        </div>
    </div>
);

export function OvulationCalculatorTool() {
  const [result, setResult] = useState<Result | null>(null);

  const form = useForm<FormValues>({
    resolver: zodResolver(FormSchema),
    defaultValues: {
      cycleLength: 28,
      day: undefined,
      month: undefined,
    },
  });

  function onSubmit(data: FormValues) {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth() + 1;

    let year = currentYear;
    if (data.month > currentMonth) {
        year = currentYear - 1;
    }

    const lastPeriodDate = new Date(year, data.month - 1, data.day);
    const { cycleLength } = data;
    
    if (isNaN(lastPeriodDate.getTime()) || lastPeriodDate > now) {
        form.setError("day", { message: "تاريخ غير صالح أو في المستقبل." });
        return;
    }

    const ovulationDate = addDays(lastPeriodDate, cycleLength - 14);
    const fertileWindowStart = subDays(ovulationDate, 5);
    const fertileWindowEnd = ovulationDate; // The fertile window ends on ovulation day
    const nextPeriodDate = addDays(lastPeriodDate, cycleLength);
    const testDay = addDays(nextPeriodDate, 1);

    const newResult = {
      lastPeriodDate,
      ovulationDate,
      fertileWindowStart,
      fertileWindowEnd,
      nextPeriodDate,
      testDay
    };

    setResult(newResult);
  }
  


  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>حاسبة التبويض</CardTitle>
        <CardDescription>توقعي أيام الخصوبة لديك لزيادة فرص الحمل.</CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
               <DateFields control={form.control} />
               <FormField name="cycleLength" control={form.control} render={({ field }) => (
                  <FormItem>
                    <FormLabel>متوسط طول الدورة (بالأيام)</FormLabel>
                    <FormControl><Input type="number" {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
              )}/>
            </div>
            <Button type="submit" className="w-full">احسبي أيام التبويض</Button>
          </form>
        </Form>
        {result && (
          <div className="mt-8">
            <h3 className="text-xl font-headline font-semibold mb-6 text-center">النتائج التقديرية</h3>

            {/* Results Cards */}
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3 mb-8">
                 <ResultInfoCard date={result.lastPeriodDate} title="بداية الحيض" colorClass="bg-red-100 border-red-500" icon={Droplet}/>
                 <ResultInfoCard date={result.fertileWindowStart} title="بداية الخصوبة" colorClass="bg-green-100 border-green-500" icon={Leaf}/>
                 <ResultInfoCard date={result.ovulationDate} title="يوم الإباضة" colorClass="bg-red-100 border-red-500" icon={Target}/>
                 <ResultInfoCard date={result.fertileWindowEnd} title="نهاية الخصوبة" colorClass="bg-green-100 border-green-500" icon={Leaf}/>
                 <ResultInfoCard date={result.testDay} title="أفضل يوم للاختبار" colorClass="bg-blue-100 border-blue-500" icon={TestTube}/>
                 <ResultInfoCard date={result.nextPeriodDate} title="الدورة القادمة" colorClass="bg-gray-100 border-gray-500" icon={CircleHelp}/>
            </div>

            {/* Calendar */}
            <OvulationCalendar
              lastPeriodDate={result.lastPeriodDate}
              ovulationDate={result.ovulationDate}
              fertileWindowStart={result.fertileWindowStart}
              fertileWindowEnd={result.fertileWindowEnd}
              nextPeriodDate={result.nextPeriodDate}
              initialMonth={result.lastPeriodDate}
              className="w-full"
            />

             <p className="text-xs text-muted-foreground mt-6 text-center">
                هذه النتائج هي مجرد تقديرات. تختلف دورة كل امرأة عن الأخرى.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
